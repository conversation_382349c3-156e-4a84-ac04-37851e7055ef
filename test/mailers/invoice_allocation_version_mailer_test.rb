require 'test_helper'

class InvoiceAllocationVersionMailerTest < ActionMailer::TestCase
  let(:recipient_emails) { ['<EMAIL>'] }
  let(:invoices) { Invoice.allocation_changed_since(1.day.ago) }

  test 'notify_changes sends an attachment' do
    mail = InvoiceAllocationVersionMailer.notify_changes(invoices)

    assert_not_empty mail.attachments
  end

  test 'notify_changes sends email to proper recipients' do
    mail = InvoiceAllocationVersionMailer.notify_changes(invoices)

    assert_equal recipient_emails, mail.to
  end

  test 'notify_changes sends email to proper snapshots' do
    assert_difference -> { ActiveSnapshot::Snapshot.count }, 1 do
      InvoiceAllocationVersionMailer.notify_changes(invoices).deliver_now
    end
  end
end
