require 'test_helper'

class Hr::HolidayRequestMailerTest < ActionMailer::TestCase
  setup do
    @holiday_request = holiday_requests(:one)
  end

  def test_holiday_request_created
    mail = Hr::HolidayRequestMailer.holiday_request_created(
      holiday_request: @holiday_request,
      receipient_email: '<EMAIL>',
      mail_to_examiner: true
    )

    string = "http://localhost:8080/#/holidays/team_calendar/#{@holiday_request.id}"
    assert_email_body_matches string, mail
  end

  def test_holiday_request_created_not_sending_email_to_locked_receipient
    applicant = @holiday_request.applicant
    applicant.locked!
    deliver = Hr::HolidayRequestMailer.holiday_request_created(
      holiday_request: @holiday_request,
      receipient_email: applicant.email,
      mail_to_examiner: true
    ).deliver_now
    refute deliver
  end

  def test_holiday_request_category_converted
    mail = Hr::HolidayRequestMailer.holiday_request_category_converted(
      holiday_request: @holiday_request,
      receipient_email: '<EMAIL>'
    )

    assert_match 'Zmieniono kategorię Twojego wniosku', mail.subject
    assert_email_body_matches(
      "Kategoria wniosku użytkownika #{@holiday_request.applicant.username} została zmieniona",
      mail
    )
  end

  def test_holiday_request_report
    email = Hr::HolidayRequestMailer.holiday_request_report
    assert_equal Settings.holiday_request_report_to, email.to
  end
end
