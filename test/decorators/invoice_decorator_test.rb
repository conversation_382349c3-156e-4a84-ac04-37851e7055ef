require 'test_helper'

class InvoiceDecoratorTest < Draper::TestCase
  setup do
    @invoice = invoices(:payment_three).decorate
  end

  test 'invoice address from the client' do
    client = clients(:arte)
    address = @invoice.address
    assert_equal client.name, address[:name]
    assert_equal client.street, address[:street]
    assert_equal client.street_number, address[:street_number]
    assert_equal client.city, address[:city]
    assert_equal client.postcode, address[:postcode]
    assert_equal client.country, address[:country]
  end

  test 'invoice address from the client address' do
    invoice = invoices(:payment_three_amendment_draft).decorate
    client_address = client_addresses(:arte)
    address = invoice.address
    assert_equal client_address.name, address[:name]
  end

  test 'invoice address from invoice itself' do
    invoice = invoices(:payment_four).decorate
    address = invoice.address
    assert_equal invoice.client_name, address[:name]
    assert_equal invoice.street, address[:street]
    assert_equal invoice.city, address[:city]
    assert_equal invoice.postcode, address[:postcode]
    assert_equal invoice.country, address[:country]
  end
end
