---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings.json?date_from=2017-01-03&date_to=2017-01-08&f%5B%5D=assigned_to_id&f%5B%5D=project_id&limit=100&op%5Bassigned_to_id%5D==&op%5Bproject_id%5D==&page=1&v%5Bassigned_to_id%5D%5B0%5D=1155&v%5Bproject_id%5D%5B0%5D=&v%5Bproject_id%5D%5B1%5D=796
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"befc74c6774a2cf3a5e5f8e564aa4bfa"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 7fb08332-6169-44a5-b749-ed62b3b45593
      X-Runtime:
      - '0.679387'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"resources":[{"id":25,"assigned_to_id":1155,"project_id":796,"issue_id":169527,"start_date":"2017-01-03T00:00:00+01:00","end_date":"2017-01-08T00:00:00+01:00","hours_per_day":8.0,"notes":".","created_at":"2023-07-17T11:01:02+02:00","updated_at":"2023-07-17T11:01:02+02:00"}],"total_count":1}'
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: delete
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings/destroy.json
    body:
      encoding: UTF-8
      string: '{"ids":[25]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json
      Cache-Control:
      - no-cache
      X-Request-Id:
      - 7201efda-278d-495d-aec0-6b494c903d1c
      X-Runtime:
      - '0.028403'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings.json
    body:
      encoding: UTF-8
      string: '{"resource_booking":{"assigned_to_id":1155,"project_id":796,"issue_id":169527,"start_date":"2017-01-04","end_date":"2017-01-09","notes":".","hours_per_day":8}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 201
      message: Created
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"0ae1eb9eaa82cc9c2e43c64ed24ee4eb"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - _redmine_session=TjZFOHdoZ2cxemVGb0lOdVNkdUU2MmtRTlhoK21QSzA0Smk1Y25vcU00azFBZG1IWEdaRExRT1RmaXdPUWczQU5INWtleENjb0I0M1JWU0trMWgzc3NtUVFRM0NQOXZ5VkQzLzhweG1ySnhuaHl5VWhPelgwN2xhc21leVNDRlVuR0M4eVVvWjBqRExFUkV6Uklvd1lNanlwU28yaWJWVFdSclJFL3hFYzF4MHBSSnlvaHdWTmwrMThYMURSUmdzUys2MWVjKzZlSHpjbDlvWVFhckNsS0hoRWMyNU1JMUFxRlQ1bHI0QzE1dG1ROVVKamZLQld6NDg3eEwzT21sanh2TXZxNVpzdUpiZWxDVXJiTGFPYWJqRjJ0UWRtMXNDdlJvYU9oTks2aFJxT0F3a0k5U3M4WElKZzc2d2oyZk5SUFRsbE5KR2NnSnFlWThQRzViM3hBSlN0WXVHT3h4Y0JDMHhJQXJmSzJTOTFlUnZnMzRHZXpCcEg2aCs2em5zcWpqNG5rTFphR2hKZnNiV2kySCtrbnVKMGZhTFVZcU0wanN6SG80NGZnVUJuc240SThaeEs3a3haYkdYRXZVZVFoeGg4dVRuRmtyK2IzUi8rYUFuUHJ6WkV6UWxaY2o5bWpTRnNValdsZTkzZFNrNXVUQ3JJRFRXZmVuMjVUcEpoWEczZDVISzFzWjNVTU5SNmY5d1dGNkU3dk5WYm9EanJaRUhSOG00ekg4PS0tSzREanR3elVQNGJXY0c1WnlEaHlUdz09--aaeb905c252d73ea97a98cca94f0e214f96b78df;
        path=/; HttpOnly
      X-Request-Id:
      - f2ca5a2b-f49c-4b57-af27-2fd9326a0572
      X-Runtime:
      - '0.665743'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"resource":{"id":26,"assigned_to_id":1155,"project_id":796,"issue_id":169527,"start_date":"2017-01-04T00:00:00+01:00","end_date":"2017-01-09T00:00:00+01:00","hours_per_day":8.0,"notes":".","created_at":"2023-07-17T11:05:46+02:00","updated_at":"2023-07-17T11:05:46+02:00"}}'
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
recorded_with: VCR 6.1.0
