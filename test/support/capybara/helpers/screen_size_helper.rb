module Capybara
  module Helpers
    module ScreenSizeHelper

      SCREENS = {
        mobile:  { width: 598 , height: 768 },
        tablet:  { width: 602 , height: 768 },
        desktop: { width: 994, height: 768 },
        large_desktop:  { width: 1024, height: 768 },
        very_large_desktop: { width: 1920, height: 1080 }
      }

      def default_size
        'very_large_desktop'
      end

      def resize_window_to(size)
        screen_size = SCREENS[size.to_sym]
        raise "Invalid screen size #{size}. It should be :mobile, :tablet, :destktop, :large_desktop or :very_large_desktop" if screen_size.blank?
        window = Capybara.current_session.driver.browser.manage.window
        window.resize_to(screen_size[:width], screen_size[:height]) if Capybara.current_session.driver.browser.respond_to? 'manage'
      end
    end
  end
end
