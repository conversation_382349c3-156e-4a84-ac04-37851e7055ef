require 'test_helper'

class Overrides::ConfirmationsControllerTest < ActionController::TestCase
  setup do
    @request.env['devise.mapping'] = Devise.mappings[:user]
  end

  test 'sends confirmation email to user' do
    user = users(:mkalita_user)
    user.update_column(:confirmed_at, nil)
    post :create, params: { user: { email: user.email } }
    assert_response 200
  end

  test 'fails to send if user already confirmed' do
    user = users(:mkalita_user)
    user.confirm
    post :create, params: { user: { email: user.email } }
    assert_response 422
  end

  test 'fails to send if user with email not found' do
    post :create, params: { user: { email: '<EMAIL>' } }
    assert_response 422
  end

  test 'confirms user' do
    user = users(:mkalita_user)
    user.update_column(:confirmed_at, nil)
    user.send_confirmation_instructions

    get :show, params: { confirmation_token: user.confirmation_token }

    assert_response 302
    assert user.reload.confirmed?
  end
end
