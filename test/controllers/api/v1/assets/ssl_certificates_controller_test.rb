require 'test_helper'

class Api::V1::Assets::SslCertificatesControllerTest < ActionController::TestCase
  setup do
    @user = users(:wik<PERSON>)
    project = projects(:one)
    authenticate(@user)
    @asset = SslCertificate.create!(project: project, name: 'budogram', ssl_type: 'encrypt',
                                    requester: @user, requested_date: Time.zone.now,
                                    notes: 'My awesome notes', domain: 'www.budogram.pl')
    @valid_params = {
      project_id: project.id,
      domain: 'www.google.com',
      ssl_type: 'encrypt'
    }
  end

  test 'asset is created given valid params' do
    assert_difference('SslCertificate.count') do
      post :create, params: { ssl_certificate: @valid_params }, format: :json
    end

    assert_response :created
  end

  test 'returns asset' do
    post :show, params: { id: @asset.id }, format: :json
    assert_equal json_body['id'], @asset.id
    assert_response :ok
  end
end
