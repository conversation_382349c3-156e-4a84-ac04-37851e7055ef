require 'test_helper'

class Api::V1::DocsFileCommentsControllerTest < ActionController::TestCase
  let(:user) { users(:wik<PERSON>) }
  let(:project) { projects(:two) }
  let(:docs_file) { docs_files(:one) }
  let(:valid_attributes) { { content: 'Example comment' } }

  setup do
    authenticate(user)
  end

  test 'GET #index' do
    get :index, params: { project_id: project, docs_file_id: docs_file }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
    assert_equal docs_file_comments(:one).content, json_body.first['content']
  end

  test 'POST #create success' do
    assert_difference -> { DocsFileComment.count } do
      post :create, params: { project_id: project, docs_file_id: docs_file,
                              docs_file_comment: valid_attributes },
                    format: :json
    end

    assert_response :created
    docs_file_comment = DocsFileComment.find(json_body['id'])
    assert_equal user, docs_file_comment.created_by
  end

  test 'POST #create failure' do
    post :create, params: { project_id: project, docs_file_id: docs_file,
                            docs_file_comment: { content: '' } },
                  format: :json

    assert_response :unprocessable_entity
  end
end
