require 'test_helper'

module Api
  module V1
    class CardsControllerTest < ActionController::TestCase
      test 'index' do
        authenticate(users(:wik<PERSON>))

        get :index, format: :json

        assert_response :success
        assert_equal Card.count, json_body.count
        assert_match users(:milosz).full_name, response.body
        assert_match users(:milosz).id.to_s, response.body
        assert_match companies(:one).id.to_s, response.body
      end
    end
  end
end
