require "test_helper"
class Api::V1::ApprovalsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  def test_index_for_agreement
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    @agreement = Agreement.create!(name: 'agreement', content: 'content', confirmation_button_text: 'button', contract_of_employment: true, departments: [departments(:one)], companies: [companies(:one)])
    @approval_one = Approval.create!(user: users(:capybara_global_admin_user), approvable_id: @agreement.id, accepted: true, approvable_type: 'Agreement')
    @approval_two = Approval.create!(user: users(:capybara_user), approvable_id: @agreement.id, accepted: true, approvable_type: 'Agreement')
    get :index, params: { agreement_id: @agreement.id, f: { query: '' }, accepted: '1' }, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, 2
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_search_in_index_for_agreement
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    @agreement_one = Agreement.create!(name: 'agreement1', content: 'content', confirmation_button_text: 'button', contract_of_employment: true, departments: [departments(:one)], companies: [companies(:one)])
    @agreement_two = Agreement.create!(name: 'agreement2', content: 'content', confirmation_button_text: 'button', contract_of_employment: true, departments: [departments(:one)], companies: [companies(:one)])
    @approval_one = Approval.create!(user: users(:capybara_global_admin_user), approvable_id: @agreement_one.id, accepted: true, approvable_type: 'Agreement')
    @approval_two = Approval.create!(user: users(:capybara_global_admin_user), approvable_id: @agreement_two.id, accepted: true, approvable_type: 'Agreement')
    get :index, params: { agreement_id: @agreement_one.id, f: { query: 'Capyb' }, accepted: '1' },
                format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, 1
    assert_equal json_body.first['first_name'], 'Capybara'
    assert_equal json_body.first['last_name'], 'Admin'
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_for_project_agreement
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    @project_agreement = ProjectAgreement.create!(project: projects(:two), company: companies(:one), business_to_business: true, content: 'content')
    @approval_one = Approval.create!(user: users(:capybara_global_admin_user), approvable_id: @project_agreement.id, accepted: true, approvable_type: 'ProjectAgreement')
    @approval_two = Approval.create!(user: users(:capybara_user), approvable_id: @project_agreement.id, accepted: true, approvable_type: 'ProjectAgreement')
    get :index, params: { project_id: projects(:two).id, f: { query: '' }, accepted: '1' },
                format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, 2
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_search_in_index_for_project_agreement
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    @project_agreement_one = ProjectAgreement.create!(project: projects(:one), company: companies(:one), business_to_business: true, content: 'content')
    @project_agreement_two = ProjectAgreement.create!(project: projects(:two), company: companies(:one), business_to_business: true, content: 'content')
    @approval_one = Approval.create!(user: users(:capybara_global_admin_user), approvable_id: @project_agreement_one.id, accepted: true, approvable_type: 'ProjectAgreement')
    @approval_two = Approval.create!(user: users(:capybara_global_admin_user), approvable_id: @project_agreement_two.id, accepted: true, approvable_type: 'ProjectAgreement')
    get :index, params: { project_id: projects(:two).id, f: { query: 'Capyb' }, accepted: '1' },
                format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, 1
    assert_equal json_body.first['first_name'], 'Capybara'
    assert_equal json_body.first['last_name'], 'Admin'
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'not_accepted' do
    get :not_accepted, format: :json

    assert_response :success
    assert_equal 1, json_body.count
  end

  test 'accept' do
    approval = approvals(:mkalita_approval)
    patch :accept, params: { id: approval.id }, format: :json

    assert_response :no_content
    assert approval.reload.accepted?
  end
end
