require 'test_helper'

class AgreementTypeUpdateWorkerTest < ActiveSupport::TestCase
  test 'perform with user_id updates agreement type for user' do
    user = users(:milosz)
    user.update(contract_of_employment: false, remote_allowed: false)

    assert_changes -> { user.reload.contract_of_employment }, from: false, to: true do
      AgreementTypeUpdateWorker.new.perform(user.id)
    end
  end

  test 'perform without user_id updates agreement type for all internal users' do
    milosz = users(:milosz)
    wiktoria = users(:wiktoria)
    milosz.update(contract_of_employment: false, remote_allowed: false)
    wiktoria.update(contract_of_employment: true)

    assert_changes -> { milosz.reload.contract_of_employment }, from: false, to: true do
      assert_changes -> { wiktoria.reload.contract_of_employment }, from: true, to: false do
        AgreementTypeUpdateWorker.new.perform
      end
    end
  end
end
