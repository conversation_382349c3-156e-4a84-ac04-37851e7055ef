require 'test_helper'

class RedmineProjectsWorkerTest < ActiveSupport::TestCase
  setup do
    @worker = RedmineProjectsWorker.new
  end

  test 'creates redmine project and sets redmine_id' do
    VCR.use_cassette('create_redmine_project') do
      project = projects(:one)
      @worker.perform(:update, project.id)
      assert project.reload.redmine_id.present?
    end
  end

  test 'updates redmine project' do
    VCR.use_cassette('update_redmine_project') do
      project = projects(:two)
      project.update_attribute(:name, 'Edited project two')
      result = @worker.perform(:update, project.id)
      assert result
    end
  end

  test 'destroys redmine project' do
    VCR.use_cassette('destroy_redmine_project') do
      project = projects(:two)
      result = @worker.perform(:destroy, project.redmine_id)
      assert result
    end
  end

  test 'archives redmine project' do
    VCR.use_cassette('archive_redmine_project') do
      project = projects(:two)
      result = @worker.perform(:archive, project.id)
      assert result
    end
  end

  test 'unarchives redmine project' do
    VCR.use_cassette('unarchive_redmine_project') do
      project = projects(:two)
      result = @worker.perform(:unarchive, project.id)
      assert result
    end
  end

  test 'closes redmine project' do
    VCR.use_cassette('close_redmine_project') do
      project = projects(:two)
      result = @worker.perform(:close, project.id)
      assert result
    end
  end

  test 'reopens redmine project' do
    VCR.use_cassette('reopen_redmine_project') do
      project = projects(:two)
      result = @worker.perform(:reopen, project.id)
      assert result
    end
  end
end
