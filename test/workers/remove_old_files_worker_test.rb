require 'test_helper'

class RemoveOldFilesWorkerTest < ActiveSupport::TestCase
  setup do
    @invoices_path = Settings.invoices_integration.files_path
    Settings.invoices_integration.files_path = Rails.root.join('test/fixtures/files/invoices_integration').to_s
    FileUtils.mkdir_p(Settings.invoices_integration.files_path)
  end

  teardown do
    FileUtils.remove_entry(Settings.invoices_integration.files_path)
    Settings.invoices_integration.files_path = @invoices_path
  end

  test 'perform' do
    # actually create cache store:
    cost_invoice = cost_invoices(:wiktoria_pending_cost_invoice)
    cost_invoice.document = File.open('test/fixtures/files/mp4.mp4')
    cost_invoice.save(validate: false)

    dir_path = Settings.invoices_integration.files_path
    old_file = File.join(dir_path, 'old.txt')
    new_file = File.join(dir_path, 'new.txt')

    File.write(old_file, 'old')
    File.write(new_file, 'new')
    File.utime(Time.now - 1.week, Time.now - 1.week, old_file)

    RemoveOldFilesWorker.new.perform

    refute File.exist?(old_file)
    assert File.exist?(new_file)
  end
end
