require 'test_helper'

class GroupMembershipTest < ActiveSupport::TestCase

  test 'validates user' do
    group_membership = GroupMembership.new
    assert group_membership.invalid?
    refute_empty group_membership.errors[:user], 'user presence should be validated'
  end

  test 'validates group' do
    group_membership = GroupMembership.new
    assert group_membership.invalid?
    refute_empty group_membership.errors[:group], 'group presence should be validated'
  end

  test 'is valid with valid parameters' do
    group_membership = GroupMembership.new(user: users(:miko<PERSON>j), group: groups(:four))
    assert group_membership.valid?
  end
end
