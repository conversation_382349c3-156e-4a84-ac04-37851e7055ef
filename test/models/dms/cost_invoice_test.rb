require 'test_helper'

module Dms
  class CostInvoiceTest < ActiveSupport::TestCase
    let(:accepting_user) { users(:wiktoria) }
    let(:comment) { 'comment' }

    subject { Dms::CostInvoice.new }

    should belong_to(:card).optional
    should validate_presence_of :kind
    should have_many(:attachments).inverse_of(:attachable).dependent(:destroy)

    setup do
      CostInvoiceAcceptance.delete_all
    end

    test 'number uniqueness is validated' do
      subject.assign_attributes(
        title: 'Cost invoice title',
        user: users(:wiktoria),
        payment_method: :cash,
        flow: :simplified,
        number: '2022/03/4',
        description: 'description',
        company: companies(:one),
        document_data: TestData.document_data,
        sell_date: 2.days.ago,
        due_date: 2.weeks.from_now.to_date,
        invoice_date: 2.days.ago,
        contractor: contractors(:board_contractor),
        currency: :PLN,
        cost_invoice_positions_attributes: [{
          name: 'Position name',
          amount: 1,
          unit_price: 1000,
          tax_rate: :'0'
        }], cost_projects_attributes: [{
          accounting_number: accounting_numbers(:one),
          department: departments(:two),
          amount: 1000
        }]
      )
      assert subject.valid?

      subject.send_to_controller
      assert subject.invalid?
      assert_equal ['an invoice with the given number and contractor already exists in the system'], subject.errors[:number]
    end

    test 'total amount consistency with cost projects is validated' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.send_to_controller
      assert cost_invoice.valid?

      cost_invoice.cost_projects.first.amount = 8_000

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:total_amount]
    end

    test 'invoice date not in the future is validated' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.invoice_date = Date.tomorrow
      assert cost_invoice.valid?

      cost_invoice.send_to_controller
      assert cost_invoice.invalid?
      assert_equal ["must be before or equal to #{Date.current}"], cost_invoice.errors[:invoice_date]
    end

    test "paid_on is validated to be less or equal than the card's expires_on" do
      card = cards(:milosz_card)
      card.update(expires_on: Time.zone.today)
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.payment_method = :card
      cost_invoice.card = card
      cost_invoice.paid_on = Time.zone.today

      assert cost_invoice.valid?

      cost_invoice.paid_on = 1.day.from_now.to_date

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:paid_on]
    end

    test 'total amount consistency with negative amount/unit price' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice_position = cost_invoice.cost_invoice_positions.to_a.first
      cost_invoice_position.amount = -1
      cost_invoice_position.net_value = -10_000
      cost_invoice.cost_projects.to_a.each { |cost_project| cost_project.amount = -5_000 }
      cost_invoice.send_to_controller

      assert cost_invoice.valid?
    end

    test 'cost_invoices_acceptances are created upon send_to_controller in project flow' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)

      assert_difference('CostInvoiceAcceptance.count', 2) do
        cost_invoice.send_to_controller!
      end

      assert cost_invoice.pending_department?
      department_ids = cost_invoice.unaccepted_cost_invoice_acceptances.reload.pluck(:department_id)
      assert_includes(department_ids, departments(:mkalita_department).id)
      assert_includes(department_ids, departments(:two).id)
      assert_equal %w[chief], cost_invoice.unaccepted_cost_invoice_acceptances.pluck(:kind).uniq
    end

    test 'cost_invoices_acceptances are created upon send_to_controller in general flow' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.update(flow: 'general')

      assert_difference('CostInvoiceAcceptance.count', 2) do
        cost_invoice.send_to_controller!
      end

      assert cost_invoice.pending_department_uber?
      department_ids = cost_invoice.unaccepted_cost_invoice_acceptances.reload.pluck(:department_id)
      assert_includes(department_ids, departments(:mkalita_department).id)
      assert_includes(department_ids, departments(:two).id)
    end

    test 'cost_invoices_acceptances are created upon send_to_controller in simplified flow' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.update(flow: 'simplified')

      assert_difference('CostInvoiceAcceptance.count', 1) do
        cost_invoice.send_to_controller!
      end

      assert cost_invoice.pending_controller?
      department_ids = cost_invoice.unaccepted_cost_invoice_acceptances.reload.pluck(:department_id)
      assert_equal([nil], department_ids)
    end

    test 'cost_invoices_acceptaces are flushed if departments are changed' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.send_to_controller!
      assert cost_invoice.update(
        cost_projects_attributes: [
          {
            id: cost_projects(:dms_cost_invoice_project_one_department_two).id,
            _destroy: true
          },
          {
            accounting_number: accounting_numbers(:one),
            department: departments(:three),
            amount: 5_000,
            cost_account_number: cost_account_numbers(:one)
          }
        ]
      )

      acceptances = cost_invoice.cost_invoice_acceptances
      assert_equal 2, acceptances.count
      assert_equal Set.new([departments(:mkalita_department).id, departments(:three).id]),
                   Set.new(acceptances.pluck(:department_id))
    end

    test 'cost_invoices_acceptaces are flushed if departments are edited' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.send_to_controller!
      assert cost_invoice.update(
        cost_projects_attributes: [
          {
            id: cost_projects(:dms_cost_invoice_project_one_department_two).id,
            department: departments(:three)
          }
        ]
      )

      acceptances = cost_invoice.cost_invoice_acceptances
      assert_equal 2, acceptances.count
      assert_equal Set.new([departments(:mkalita_department).id, departments(:three).id]),
                   Set.new(acceptances.pluck(:department_id))
    end

    test 'accept does not change state if in pending_department and not all department acceptances are done' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.send_to_controller!

      cost_invoice.accept(accepting_user, comment)

      expect(cost_invoice.pending_department?)
    end

    test 'accept moves the state to pending_department_user if in pending_department and all acceptances are done' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.send_to_controller!
      cost_invoice.unaccepted_cost_invoice_acceptances.update_all(accepted_at: Time.zone.now)

      cost_invoice.accept(accepting_user, comment)

      expect(cost_invoice.pending_department_uber?)
      expect(cost_invoice.unaccepted_cost_invoice_acceptances.reload.any?)
    end

    test 'accept notifier worker is enqueued upon acceptation' do
      cost_invoice = cost_invoices(:dms_pending_cost_invoice)

      assert_difference -> { Dms::AcceptedNotifierWorker.jobs.count } do
        cost_invoice.accept!
      end
    end

    test 'reject notifier worker is enqueued upon rejection' do
      cost_invoice = cost_invoices(:dms_pending_cost_invoice)

      assert_difference -> { Dms::RejectedNotifierWorker.jobs.count } do
        cost_invoice.reject!
      end
    end

    test 'accept moves the state to pending_controller if in pending_department_uber' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.update(flow: 'general')
      cost_invoice.send_to_controller!

      cost_invoice.accept!(accepting_user, comment)

      expect(cost_invoice.pending_controller?)
    end

    test 'accept removes all chief acceptances if in pending_department_uber' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.update(flow: 'general')
      cost_invoice.send_to_controller!

      cost_invoice.accept!(accepting_user, comment)

      assert_empty cost_invoice.unaccepted_cost_invoice_acceptances.chief.reload
    end

    test 'accept validates contractor is accepted if in pending_controller' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.update(flow: 'simplified')
      cost_invoice.send_to_controller!
      cost_invoice.contractor.update(state: :pending)

      cost_invoice.accept!(accepting_user, comment)

      assert_not_empty cost_invoice.errors[:contractor]
      assert cost_invoice.reload.pending_controller?
    end

    test 'accept moves the state to accepted if in pending_controller' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.update(flow: 'simplified')
      cost_invoice.send_to_controller!

      cost_invoice.accept!(accepting_user, comment)

      expect(cost_invoice.accepted?)
    end

    test 'recall moves the state from accepted to pending_controller if the date is right' do
      cost_invoice = cost_invoices(:dms_accepted_cost_invoice)
      cost_invoice.sent = true

      assert_not cost_invoice.recall

      cost_invoice.accepted_at = Time.zone.now

      assert cost_invoice.recall!
      assert cost_invoice.pending_controller?
      assert_nil cost_invoice.accepted_at
      assert_not cost_invoice.sent?
      assert cost_invoice.cost_invoice_acceptances.any?
    end

    test 'create_snapshot creates snapshot with snapshot items' do
      cost_invoice = cost_invoices(:dms_pending_cost_invoice)
      state = cost_invoice.state
      action = :action

      assert_difference -> { cost_invoice.snapshots.count }, 1 do
        cost_invoice.create_snapshot(accepting_user, action, comment: comment)
      end

      snapshot = cost_invoice.snapshots.last
      assert_equal cost_invoice, snapshot.item
      assert_equal accepting_user, snapshot.user
      assert_equal ({ action: action, comment: comment, state_was: state }), snapshot.metadata

      assert_equal 3, snapshot.snapshot_items.count
      cost_invoice_item, _ = snapshot.fetch_reified_items
      assert_equal cost_invoice, cost_invoice_item
    end

    test 'create_snapshot creates snapshot children' do
      cost_invoice = cost_invoices(:dms_pending_cost_invoice)
      cost_invoice.create_snapshot(accepting_user, :action, comment: comment)

      snapshot = cost_invoice.snapshots.last
      _, children_hash = snapshot.fetch_reified_items
      assert_equal cost_invoice.cost_invoice_positions, children_hash[:cost_invoice_positions]
      assert_equal cost_invoice.cost_projects, children_hash[:cost_projects]
    end

    test 'issuer_comment validation' do
      cost_invoice = cost_invoices(:dms_pending_cost_invoice)
      cost_invoice.update(issuer_comment: 'a' * 360)
      assert cost_invoice.valid?

      cost_invoice.update(issuer_comment: 'a' * 361)
      assert_not cost_invoice.valid?
      assert_includes cost_invoice.errors[:issuer_comment], 'is too long (maximum is 360 characters)'

      cost_invoice.update(issuer_comment: nil)
      assert cost_invoice.valid?
    end

    test 'overtime cost project changes require global admin role' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.overtime_invoice = true
      cost_invoice.current_user = users(:wiktoria)

      cost_invoice.cost_projects.build(
        accounting_number: accounting_numbers(:one),
        department: departments(:two),
        amount: 1000
      )

      assert cost_invoice.invalid?
    end

    test 'overtime cost project changes allowed for global admin' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.overtime_invoice = true
      cost_invoice.current_user = users(:mkalita_user)

      cost_invoice.cost_projects.build(
        accounting_number: accounting_numbers(:one),
        department: departments(:two),
        amount: 1000
      )

      cost_invoice.valid?
    end

    test 'non-overtime cost project changes allowed for any user' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)
      cost_invoice.overtime_invoice = false
      cost_invoice.current_user = users(:wiktoria)

      cost_invoice.cost_projects.build(
        accounting_number: accounting_numbers(:one),
        department: departments(:two),
        amount: 1000
      )

      cost_invoice.valid?
    end
  end
end
