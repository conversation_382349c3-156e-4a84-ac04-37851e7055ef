# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  mpk_number: motion_design
  payment: one
  amount: 500

project_one_today:
  mpk_number: gui
  payment: project_one_today
  amount: 1000

two:
  mpk_number: gui
  payment: two
  amount: 500

three:
  mpk_number: gui
  payment: three
  amount: 250

three_shared:
  mpk_number: gui
  payment: three
  amount: 250
  project: three

four:
  mpk_number: gui
  payment: four
  amount: 500

five:
  mpk_number: gui
  payment: five
  amount: 1000

six:
  mpk_number: gui
  payment: six
  amount: 100

seven_cyclic_payment:
  mpk_number: gui
  payment: seven_cyclic_payment
  amount: 500

eight_cyclic_payment:
  mpk_number: gui
  payment: eight_cyclic_payment
  amount: 500

nine_cyclic_payment:
  mpk_number: gui
  payment: nine_cyclic_payment
  amount: 1000

tenth_cyclic_payment:
  mpk_number: gui
  payment: tenth_cyclic_payment
  amount: 1000

tenth_cyclic_child_payment:
  mpk_number: gui
  payment: tenth_cyclic_child_payment
  amount: 1000

ten:
  mpk_number: gui
  payment: ten
  amount: 500

project_five_payment:
  mpk_number: gui
  payment: project_five_payment
  amount: 500

project_five_second_payment:
  mpk_number: gui
  payment: project_five_second_payment
  amount: 500
