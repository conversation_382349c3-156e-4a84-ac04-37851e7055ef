# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

server_one:
  type: Server
  project: one
  requested_date: 2019-11-05
  requester: mkalita_user
  passed_to_execution_by: mkalita_user
  expiry_date: 2019-11-05
  notes: MyText
  technology: no_tech
  machine_type: vm
  hosting: on_site
  environment: dev
  ram_count: 5
  storage: 5
  backup: standard
  db_type: mysql
  db_size: 5
  send_emails: true
  receivers_addresses: '<EMAIL>'

server_two:
  type: Server
  project: two
  requested_date: 2019-11-05
  requester: mkalita_user
  activated_by: mkalita_user
  activated_date: 2019-11-05
  expiry_date: 2019-11-05
  notes: MyText
  technology: no_tech
  machine_type: vm
  hosting: on_site
  environment: dev
  ram_count: 5
  storage: 5
  backup: standard
  db_type: mysql
  db_size: 5
  host: example2.non.3dart.com
