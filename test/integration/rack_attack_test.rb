require 'test_helper'
include Warden::Test::Helpers

class RackAttackTest < ActionDispatch::IntegrationTest
  setup do
    @redis_cache_store = ActiveSupport::Cache::RedisCacheStore.new
    Rack::Attack.cache.store = @redis_cache_store
    Rack::Attack.throttle('req/ip', limit: 1, period: 10, &:ip)
    Rack::Attack.whitelist('allow to /api/check_absences/check') do |req|
      req.path.to_s.match(%r{/api/check_absences/check}) && req.post?
    end
    clear_cache
  end

  teardown do
    clear_cache
    ActionController::Base.perform_caching = false
  end

  def clear_cache
    @redis_cache_store.clear
  rescue Errno::ENOTEMPTY
  end

  def test_requests_over_limit_allowed_for_check_absences_check
    2.times do |i|
      post '/api/check_absences/check.json',
           params: { dates: '2016-07-20,2016-07-21,2016-07-22,2016-07-23,2016-07-24' },
           headers: { 'X-Redmine-Absences-Check-API-Key' => Settings.redmine_api['redmine_absences_check_api_key'] }
      assert_response 200, @response.body.to_s
    end
  end

  def test_requests_under_limit
    get '/api/users', headers: { 'X-Swagger-Sign-In-As' => users(:mkalita_user).id.to_s }, as: :json
    assert_response 200
  end

  def test_requests_over_limit_logged_in
    user = users(:mkalita_user)
    2.times do |i|
      get '/api/users', headers: { 'X-Swagger-Sign-In-As' => user.id.to_s },
                        as: :json
      if i == 1
        assert_response 429
        assert_equal json_body, 'error' => 'Request limit reached. Retry later.'
      end
    end
    logout
  end

  def test_requests_over_limit_not_logged_in
    2.times do |i|
      get '/api/users', as: :json
      if i == 1
        assert_response 429
        assert_equal json_body, 'error' => 'Request limit reached. Retry later.'
      end
    end
  end
end
