require 'test_helper'
require 'owncloud_provider'

class OwncloudProviderTest < ActiveSupport::TestCase
  let(:provider) { OwncloudProvider.new(share_name) }
  let(:share_name) { 'nextcloud' }

  test 'takes config from specified place' do
    assert_equal Settings[share_name][:username], provider.config[:username]
    assert_equal Settings[share_name][:password], provider.config[:password]
    assert_equal Settings[share_name][:url], provider.config[:url]
  end

  test 'takes optional subdirectories list in create_folder method' do
    response = mock('response')
    response.stubs(:code).returns(201)

    provider.expects(:call_api)
            .with(:mkcol, '/remote.php/webdav/projects/main_directory/')
            .returns(response)
    provider.expects(:call_api)
            .with(:mkcol, '/remote.php/webdav/projects/main_directory/subdirectory/')
            .returns(response)

    provider.create_directory('main_directory', 23, subdirectories: ['subdirectory'])
  end

  test 'creates employees report' do
    report = 'some_text'
    path = '/remote.php/webdav/projects/projekty-dokumenty/kosztorys_stanowiska/'\
           'active_employees_report.csv'
    provider.expects(:call_api).with(:put, path, report).once.returns(true)
    assert provider.send_employees_report(report)
  end

  test 'updates directory share' do
    permissions = 31
    identifier = 'eurocash-cash-carry-uiux'
    share_id = '52296'
    get_share_id_url = "#{Settings[share_name][:url]}/ocs/v1.php/apps/files_sharing/api/v1/shares?"\
                       "path=projects/#{identifier}"
    update_share_url = "#{Settings[share_name][:url]}/ocs/v1.php/apps/files_sharing/api/v1/shares/"\
                       "#{share_id}"

    stub_request(:get, get_share_id_url).with(
      headers: { 'OCS-APIRequest' => 'true' },
      basic_auth: [Settings[share_name][:username], Settings[share_name][:password]]
    ).to_return(
      body: File.new(Rails.root.join('test/fixtures/files/owncloud/get_share_id_response.xml')),
      status: 200
    )

    stub_request(:put, update_share_url).with(
      headers: { 'OCS-APIRequest' => 'true' },
      basic_auth: [Settings[share_name][:username], Settings[share_name][:password]],
      body: "permissions=#{permissions}"
    ).to_return(
      body: File.new(Rails.root.join('test/fixtures/files/owncloud/update_share_response.xml')),
      status: 200,
      headers: { 'Content-Type' => 'application/xml' }
    )

    provider.update_directory_share(identifier, permissions)

    assert_requested(:get, get_share_id_url)
    assert_requested(:put, update_share_url)
  end

  test 'shares folder' do
    identifier = 'eurocash-cash-carry-uiux'
    permissions = 31
    get_group_url = "#{Settings[share_name][:url]}/ocs/v1.php/cloud/groups/#{identifier}"
    share_folder_url = "#{Settings[share_name][:url]}/ocs/v1.php/apps/files_sharing/api/v1/shares"
    data = "path=projects%2F#{identifier}&shareType=1&shareWith=#{identifier}"\
           "&permissions=#{permissions}"

    stub_request(:get, get_group_url).with(
      headers: { 'OCS-APIRequest' => 'true' },
      basic_auth: [Settings[share_name][:username], Settings[share_name][:password]]
    ).to_return(
      body: File.new(Rails.root.join('test/fixtures/files/owncloud/get_group_response.xml')),
      status: 200
    )

    stub_request(:post, share_folder_url).with(
      headers: { 'OCS-APIRequest' => 'true' },
      basic_auth: [Settings[share_name][:username], Settings[share_name][:password]],
      body: data
    ).to_return(
      body: File.new(Rails.root.join('test/fixtures/files/owncloud/update_share_response.xml')),
      status: 200
    )

    provider.share_folder(identifier, permissions)

    assert_requested(:get, get_group_url)
    assert_requested(:post, share_folder_url)
  end

  test 'get_project_files' do
    identifier = 'agora-komunikaty-pl-portal'
    folder_name = DocsFile.categories.keys.second
    url = "#{Settings[share_name][:url]}/remote.php/webdav/projects/" \
          "#{ERB::Util.url_encode(identifier)}/#{ERB::Util.url_encode(folder_name)}"

    stub_request(:propfind, url).with(
      headers: { 'OCS-APIRequest' => 'true' },
      basic_auth: [Settings[share_name][:username], Settings[share_name][:password]],
      body: OwncloudProvider::PROPFIND_PROPERTIES_BODY
    ).to_return(
      body: File.new(Rails.root.join('test/fixtures/files/owncloud/propfind_response.xml'))
    )

    files_info = provider.get_project_files(identifier, folder_name)
    assert_equal 11, files_info.count
    assert_equal '116802', files_info.first.id
    assert_equal 'Artegence 22 04 2021 ŁS_v.1_J.K-K.M_v.2604.docx', files_info.first.file_name
  end

  test 'send_file works properly' do
    path = 'path'
    file = Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'))
    dir_url = "#{Settings[share_name][:url]}/remote.php/webdav/projects/#{path}/"
    file_url = File.join(dir_url, ERB::Util.url_encode(file.original_filename))

    stub_request(:mkcol, dir_url).with(
      headers: { 'OCS-APIRequest' => 'true' },
      basic_auth: [Settings[share_name][:username], Settings[share_name][:password]]
    ).to_return(
      status: 201, body: ''
    )

    stub_request(:put, file_url).with(
      headers: { 'OCS-APIRequest' => 'true' },
      basic_auth: [Settings[share_name][:username], Settings[share_name][:password]],
      body: file.read
    ).to_return(
      status: 201, body: ''
    )

    file.rewind

    provider.send_file(file, path, file.original_filename)

    assert_requested(:mkcol, dir_url)
    assert_requested(:put, file_url)
  end
end
