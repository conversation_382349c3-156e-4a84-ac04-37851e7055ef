require 'test_helper'

# tail -n 20000 -f log/test.log | grep -A55 "ProjectPolicyTest: test_create"
class PositionPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { Position }
  let(:regular_record) do
    regular_record = Position.create(name: 'My test position')
  end
  let(:nil_user) do
    nil
  end
  let(:imperator_programmer_user) do
    user = User.create!(email: '<EMAIL>',
                       username: 'imperator_programmer_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_admin_programmer)
    user
  end
  let(:admin_user) do
    user = User.create!(email: '<EMAIL>',
                       username: 'admin_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_admin)
    user
  end
  let(:signed_in_user) do
    user = User.create!(email: '<EMAIL>',
                       username: 'record_user_gmail',
                       first_name: 'Adam',
                       last_name: '<PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_user)
    membership_params = { project_id: projects(:mkalita_project).id,
                          member_id: user.id,
                          member_type: 'User',
                          role_ids: [roles(:mkalita_role).id] }
    Membership.create!(membership_params)
    user.reload
  end

  def test_index
    assert_permit imperator_programmer_user, record_model
    assert_permit admin_user, record_model
    refute_permit signed_in_user, record_model
    refute_permit nil_user, record_model
  end

  def test_create
    assert_permit imperator_programmer_user, record_model
    assert_permit admin_user, record_model
    refute_permit signed_in_user, record_model
    refute_permit nil_user, record_model
  end

  def test_update
    assert_permit imperator_programmer_user, record_model
    assert_permit admin_user, record_model
    refute_permit signed_in_user, record_model
    refute_permit nil_user, record_model
  end

  def test_show
    assert_permit imperator_programmer_user, record_model
    assert_permit admin_user, record_model
    refute_permit signed_in_user, record_model
    refute_permit nil_user, record_model
  end

  def test_destroy
    assert_permit imperator_programmer_user, record_model
    assert_permit admin_user, record_model
    refute_permit signed_in_user, record_model
    refute_permit nil_user, record_model
  end

end
