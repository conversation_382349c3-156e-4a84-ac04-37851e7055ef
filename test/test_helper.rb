unless ENV['NO_COVERAGE'].to_s == 'true'
  require 'simplecov'
  SimpleCov.minimum_coverage 75
  SimpleCov.maximum_coverage_drop 1
  SimpleCov.start :rails do
    add_filter 'cache/bundler'
    add_filter '/lib/tasks/'
    add_filter '/lib/redmine_database.rb'
    add_filter '/lib/redmine_database/'
    add_filter '/bin/'
    add_filter 'Rakefile'
    add_filter '/script/'
    add_filter '/cache/'
    add_filter '/lib/generators/'

    add_group 'Decorators', '/app/decorators'
  end
end

require 'rails_helper'
require 'minitest-spec-context'
# require 'minitest/reporters'

# Minitest::Reporters.use! Minitest::Reporters::DefaultReporter.new

Dir[Rails.root.join('test/support/*.rb')].each { |f| require f }
Dir[Rails.root.join('test/support/helpers/*.rb')].each { |f| require f }
