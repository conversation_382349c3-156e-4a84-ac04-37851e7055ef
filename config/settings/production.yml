secret_key_base: 4f7ca6fcbff16d3def2f93481b138dab1259c2985012ed5ef5e99fd892978770433b3b7bd5b6ca672b281a54dca67533d5875770599101a7c99ab52cac997790
redmine_api:
  uri: 'http://local.non.3dart.com:3001/imperator_api/v1' # 'http://127.0.0.1:8080/imperator_api/v1'
  # basic_auth:
  #   username: redmine
  #   password: '87AeOW_Rpr%@VAEy'
  imperator_api_key: '2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0'
  redmine_api_key: '2a015af0aaa2e0b525831b9c2f238b14311bf012'
  redmine_absences_check_api_key: '21775217f0ef4b8e56ce51a3213fdsdfaga3423'
  # no_proxy: true
  # host_header: 'redmine.dev.non.3dart.com'
click_up_api:
  uri: 'https://api.clickup.com/api/v2'
  client_id: '5ZSN76RCUS4TWSXQV3QE4X9017V26YL2A'
  client_secret: 'XB2MP245IKOA28NVD48BYF3WSSJ8H20NHRJVF74VTPB8Y0EU9MGF32PFXHI6D61HA'
  api_key: 'pk_188448912_CEZLYGB9AVK1U5UOM12R1RA1Y5105TIRA'
space_cooperative_project_id: 458213548476
nbp_api: 'https://api.nbp.pl/api/exchangerates'
mailer:
  from: "no-reply@localhost"
  default_url_options:
    host: "imperator.dev.non.3dart.com"
    protocol: "http"
  smtp_settings:
   address: "localhost"
   port: 25
   enable_starttls_auto: false
   openssl_verify_mode: "none" # Only use this option for a self-signed and/or wildcard certificate
exception_notification:
  email_prefix: "[IMPERATOR] "
  sender_address: "exception.notifier@localhost"
  exception_recipients: <%= %w( <EMAIL> <EMAIL> ) %>
  background_sections:
    - backtrace
    - sidekiq_env
use_proxy: true
proxy:
  protocol: "http://"
  address: repo.non.3dart.com
  port: 3128
memcached_enabled: true
memcached:
  hosts:
    - '127.0.0.1:11211'
  namespace: imperator
  compress: true
  failover: true
  socket_timeout: 1.5
  socket_failure_delay: 0.2
redis_enabled: false
redis:
  url: 'redis://localhost:6379/3'
# Swagger
swagger_host: 'imperator.dev.non.3dart.com'
swagger:
  api_url_base: 'http://imperator.dev.non.3dart.com'
allow_impersonification_without_authentication: true # DANGER! use `true` only for development or test server!
overwrite_mail_to: true
# Production only
owncloud:
  url: http://cloudshare.non.3dart.com
  username: redmine
  password: y9nB33nrpi3V
disable_hr_workers:
  disable_absence_quota_worker: false
  disable_holiday_request_reminder_worker: false
  disable_holiday_request_convert_sick_to_vacation_worker: false
  disable_holiday_request_postponed_reminder_worker: false
features_management_enabled: true
features_management_login: 'admin'
features_management_password: '39b3de58ef9fc451cc53d881a005460c'
impersonation_enabled: true
impersonation_login: 'admin'
impersonation_password: '75004d8c664638ff40fda8f378b4f70d'
sidekiq_username: sidekiq
sidekiq_password: '75960beef4f6584c26c4406092d1ce1a'
rabbit_enabled: false # audit, wifi token
rabbit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
#####
# dev55
#####
# rabbit:
#   connection:
#     host: hq-mq.non.3dart.com
#     vhost: mq
#     user: imperator
#     pass: [PUT PASSWORD HERE]
#   node_uuid: 1
#   app_id: <EMAIL>
audit_enabled: true # if rabbit disabled, it fallbacks to sidekiq logger
audit_exchange: 'audit'
rabbit_audit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
#####
# dev55
#####
# rabbit_audit:
#   connection:
#     host: dev124
#     vhost: mq
#     user: admin
#     pass: admin
#   node_uuid: 1
#   app_id: <EMAIL>
#   virtual_host: mq
holiday_projects: [3891, 3890]
holiday_accounting_numbers: [707, 706, 101, 102]
inbox_client_id: 2817
payment_schedules_lock_day: 2
global_hr_manager_id: 6
log_files_names: [sidekiq-new.log]
