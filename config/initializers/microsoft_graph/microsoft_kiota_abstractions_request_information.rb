# https://github.com/microsoft/kiota-abstractions-ruby/issues/8
# https://github.com/microsoft/kiota-serialization-json-ruby/issues/20

MicrosoftKiotaAbstractions::RequestInformation.class_eval do
  # override method defined in microsoft_kiota_abstractions-0.14.3/lib/microsoft_kiota_abstractions/request_information.rb:94
  def set_content_from_parsable(request_adapter, content_type, values)
    writer = request_adapter.get_serialization_writer_factory.get_serialization_writer(content_type)
    @headers.try_add(self.class.class_variable_get(:@@content_type_header), content_type)
    @content = if !values.nil? && values.is_a?(Array)
                 writer.write_collection_of_object_values(nil, values).map(&:get_serialized_content).to_json
               else
                 writer.write_object_value(nil, values).get_serialized_content
               end
  rescue StandardError
    raise 'could not serialize payload'
  end
end
