module Api
  module V1
    class GlobalRolesController < ApiController
      include ::Api::V1::Concerns::Documentation::GlobalRolesEndpoint

      def index
        authorize(GlobalRole, :index?)
        results = search(scope: global_roles_scope, filters: params[:f]).results
        request.variant = :collection_for_select if params[:f].try(:[], :collection_for_select).to_s == 'true'
        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              @global_roles = authorize!(results)
              render template: '/api/v1/global_roles/index', collection: @global_roles
            end
            variant.none do
              @global_roles = paginate(authorize!(results), search_options(params))
              raise ActiveRecord::RecordNotFound, 'Page not found' if params[:page].to_i > 1 && @global_roles.empty?
            end
          end
        end
      end

      def show
        @global_role = authorize!(find_global_role)
        respond_with(@global_role.decorate)
      end

      def create
        authorize!(GlobalRole)
        respond_with(GlobalRole.create(global_role_params).decorate)
      end

      def update
        @global_role = authorize!(find_global_role)
        @global_role.update(global_role_params)
        respond_with(@global_role.decorate)
      end

      def update
        @global_role = authorize!(find_global_role)
        @global_role.update(global_role_params)
        respond_with(@global_role.decorate)
      end

      def destroy
        @global_role = authorize!(find_global_role)
        @global_role.destroy
        respond_with(@global_role)
      end

      private

      def search(options = {})
        @search = ::Searches::GlobalRoleSearch.new(search_options(options))
      end

      def global_roles_scope
        GlobalRole.all
      end

      def find_global_role
        GlobalRole.find(params[:id])
      end

      def find_global_roles
        global_roles_scope.find(params[:ids])
      end

      def global_role_params
        params.require(:global_role).permit(policy(GlobalRole).permitted_attributes)
      end
    end
  end
end
