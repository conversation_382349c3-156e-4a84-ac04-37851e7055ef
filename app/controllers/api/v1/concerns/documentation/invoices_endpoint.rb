module Api
  module V1
    module Concerns
      module Documentation
        module InvoicesEndpoint
          include Swagger::Blocks
          extend ActiveSupport::Concern

          included do
            swagger_path '/api/projects/{project_id}/invoices' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :project_id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :get do
                key :description, "Provides a list of project's invoices"
                key :operationId, 'getInvoices'
                key :tags, %w(invoices payment_schedules)
                response 200 do
                  key :description, 'invoices list response'
                  schema do
                    key :type, :array
                    items do
                      key :'$ref', :InvoiceResponses
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 403 do
                  key :description, 'Forbidden'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
              operation :post do
                key :description, 'Adds an invoice to payment schedule'
                key :operationId, 'createInvoice'
                key :tags, %w(invoices payment_schedules)
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                      JSON form data for Invoice to add
                                      Editable: title, mpk_positions
                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :InvoiceRequest
                  end
                end
                response 201 do
                  key :description, 'Response with Invoice'
                  schema do
                    key :'$ref', :InvoiceResponse
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 403 do
                  key :description, 'Forbidden'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{project_id}/invoices/form_data/{payment_id}' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :project_id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              parameter do
                key :name, :payment_id
                key :in, :path
                key :description, 'ID of payment'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :get do
                key :description, 'Provides a list of revenue accounts and mpk numbers needed for invoice form'
                key :operationId, 'getInvoiceFormData'
                key :tags, %w(invoices payment_schedules)
                response 200 do
                  key :description, 'invoice form data response'
                  schema do
                    key :revenue_accounts, :object do
                      key :type, :array
                      items do
                        key :'$ref', :RevenueAccountResponses
                      end
                    end
                    key :mpk_numbers, :object do
                      key :type, :array
                      items do
                        key :'$ref', :MpkNumberResponses
                      end
                    end
                  end
                end
              end
            end
            swagger_path '/api/projects/{project_id}/invoices/{id}' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :project_id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of invoice'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :patch do
                key :description, 'Update Invoice'
                key :operationId, 'updateInvoice'
                key :tags, %w(invoices payment_schedules)
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    JSON form data for Invoice to update;
                                    Editable: title, mpk_positions
                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :InvoiceRequest
                  end
                end
                response 204 do
                  key :description, 'invoice updated'
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 403 do
                  key :description, 'Forbidden'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
