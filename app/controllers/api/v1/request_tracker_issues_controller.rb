module Api
  module V1
    class RequestTrackerIssuesController < ApplicationController
      def create
        authorize(RequestTrackerIssue, :create?)
        request = RequestTrackerIssue.new(request_tracker_issue_params)
        if request.valid?
          send_mail(request)
          head :no_content
        else
          render json: { errors: request.errors.messages }, status: :unprocessable_entity
        end
      end

      private

      def send_mail(request)
        AdminRequestMailer.notify_admin(request, current_user).deliver_now
      end

      def request_tracker_issue_params
        params.require(:mail_props)
              .permit(:title, :content, :project_id, :cc, attachments_info: %i[location filename])
      end
    end
  end
end
