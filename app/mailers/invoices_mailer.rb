class InvoicesMailer < PaymentSchedulesMailer
  def processed_invoices(xlsx_doc, file_name)
    attachments[file_name] = xlsx_doc
    recipients = processed_invoices_recipients

    mail(to: recipients, subject: 'Cotygodniowy raport z faktur') if recipients.present?
  end

  private

  def processed_invoices_recipients
    User.active.includes(:global_roles).where(global_roles: { processed_invoices_notification: true })
        .pluck(:email).uniq
  end
end
