module Hr
  class HolidayRequestMailer < ApplicationMailer
    def holiday_request_created(opts = {})
      common_config(opts)
      holiday_request_created_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_created_and_autoaccepted(opts = {})
      common_config(opts)
      holiday_request_created_and_autoaccepted_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_reminder(opts = {})
      common_config(opts)
      holiday_request_reminder_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_postponed_reminder(opts = {})
      common_config(opts)
      @holiday_requests = HolidayRequest.where(id: opts.fetch(:holiday_request_ids)).order('holiday_requests.starts_on').includes(:applicant).to_a
      headers 'X-Holiday-Request-Ids' => @holiday_requests.map(&:id).join(',')
      holiday_request_postponed_reminder_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_updated(opts = {})
      common_config(opts)
      holiday_request_updated_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_destroyed(opts = {})
      common_config(opts)
      holiday_request_destroyed_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_updated_and_autoaccepted(opts = {})
      common_config(opts)
      holiday_request_updated_and_autoaccepted_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_category_converted(opts = {})
      common_config(opts)
      holiday_request_category_converted_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_convert_sick_to_vacation(opts = {})
      common_config(opts)
      holiday_request_convert_sick_to_vacation_config
      send_mail unless receipient_locked?
      save_notification_as_dispatched
    end

    def holiday_request_report
      time = Time.zone.now
      @year = time.year
      @month = time.month

      Company.native.each do |company|
        doc = HolidaysReportXlsGenerator.generate(company)
        attachment_filename = "raport_#{company.name}.xls"
        attachments[attachment_filename] = doc.read
      end

      mail to: Settings.holiday_request_report_to, subject: "Raport urlopów za #{@year}-#{@month}"
    end

    private

    def receipient_locked?
      User.where(email: @receipient_email).locked.exists?
    end

    def save_notification_as_dispatched
      return unless @notification_id

      notification = Notification.find(@notification_id)
      notification.title = @subject
      notification.acknowledge_dispatch!
      notification.save(validate: false)
    end

    def common_config(opts)
      @receipient_email = opts.fetch(:receipient_email)
      unless opts[:holiday_request_ids]
        @holiday_request = opts.fetch(:holiday_request)
        @applicant_username = @holiday_request.applicant.try(:username) || ''
      end
      @mail_to_examiner = opts[:mail_to_examiner] || false
      @notification_id = opts[:notification_id]
      @is_expiring = opts[:is_expiring] || false
      @status_change = opts[:status_change] && opts[:status_change].to_sym
    end

    def send_mail
      raise '@subject not specified' if @subject.nil?

      mail(to: @receipient_email, subject: @subject)
    end

    def holiday_request_created_config
      @subject = "Utworzono wniosek niedostępności użytkownika #{@applicant_username}, oczekuje na akceptację"
    end

    def holiday_request_created_and_autoaccepted_config
      @subject = if @holiday_request.category == 'Niedostępność/Ch'
                   "Ustalono niedostępność użytkownika #{@applicant_username} z powodu choroby"
                 elsif @holiday_request.applicant&.board_member?
                   "Ustalono niedostępność użytkownika #{@applicant_username} (członek zarządu)"
                 else # 'Niedostępność/O'
                   "Ustalono niedostępność użytkownika #{@applicant_username}"
                 end
    end

    def holiday_request_reminder_config
      @subject = "Wniosek niedostępności użytkownika #{@applicant_username} oczekuje na akceptację (przypomnienie)"
    end

    def holiday_request_postponed_reminder_config
      @subject = 'Niedostępności w Twoich projektach w przyszłym tygodniu (przypomnienie)'
    end

    # rubocop:disable Metrics/MethodLength
    def holiday_request_updated_config
      @outcome = if @status_change == :accept
                   'zaakceptowany' # 'accepted'
                 elsif @status_change == :reject
                   'odrzucony' # 'rejected'
                 elsif @holiday_request.pending?
                   'zaktualizowny, oczekuje na akceptację' # 'updated'
                 else
                   'zaktualizowny' # 'updated'
                 end
      @subject =
        if @mail_to_examiner
          "Wniosek niedostępności użytkownika #{@applicant_username} został #{@outcome}"
        else
          "#{@applicant_username} - Twój wniosek niedostępności został #{@outcome}"
        end
    end
    # rubocop:enable Metrics/MethodLength

    # rubocop:disable Layout/LineLength
    def holiday_request_destroyed_config
      @outcome = 'usunięty'
      @subject = if @mail_to_examiner
                   if @holiday_request.category == 'Niedostępność/Ch'
                     "Wniosek niedostępności użytkownika #{@applicant_username} z powodu choroby został #{@outcome}"
                   elsif @holiday_request.category == 'Niedostępność/O'
                     "Wniosek niedostępności użytkownika użytkownika #{@applicant_username} z tytułu okoliczności został #{@outcome}"
                   else
                     "Wniosek niedostępności użytkownika użytkownika #{@applicant_username} został #{@outcome}"
                   end
                 else
                   "#{@applicant_username} - Twój wniosek niedostępności został #{@outcome}"
                 end
    end
    # rubocop:enable Layout/LineLength

    def holiday_request_updated_and_autoaccepted_config
      @subject = if @holiday_request.category == 'Niedostępność/Ch'
                   "Zaktualizowano niedostępność użytkownika #{@applicant_username} z powodu choroby"
                 elsif @holiday_request.applicant&.board_member?
                   "Zaktualizowano niedostępność użytkownika #{@applicant_username} (członek zarządu)"
                 else # 'Niedostępność/O'
                   "Ustalenia co do niedostępności użytkownika #{@applicant_username} zostały zaktualizowne"
                 end
    end

    def holiday_request_category_converted_config
      @subject = if @holiday_request.category == 'Niedostępność/Ch'
                   'Zmieniono kategorię Twojego wniosku na `Niedostępność/Ch`'
                 elsif @holiday_request.category == 'Niedostępność/O'
                   'Zmieniono kategorię Twojego wniosku na `Niedostępność/O`'
                 else # nie powinno sie zdarzyc
                   'Zmieniono kategorię Twojego wniosku'
                 end
    end

    def holiday_request_convert_sick_to_vacation_config
      @subject = if @holiday_request.category == 'Niedostępność'
                   "System zmienił kategorię wniosku użytkownika #{@applicant_username} na `Niedostępność`"
                 else # nie powinno sie zdarzyc
                   "System zmienił kategorię wniosku użytkownika #{@applicant_username}"
                 end
    end
  end
end
