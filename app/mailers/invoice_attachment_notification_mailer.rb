class InvoiceAttachmentNotificationMailer < ApplicationMailer
  def notify_attachment(invoice, users)
    @invoice = invoice

    mail(to: users.pluck(:email), subject: 'Acceptance protocol is required')

    invoice.create_snapshot(nil, :email,
                            comment: "Email regarding the requires file on acceptance protocol
                                      has been sent to: #{users.pluck(:email).join(', ')}")
  end

  def notify_chief(chief, invoices)
    @invoices = invoices

    mail(to: chief.email, subject: 'Invoices with no acceptance protocol')

    invoices.each do |invoice|
      invoice.create_snapshot(nil, :email,
                              comment: "Email to notify Chief of the require an acceptance protocol
                                        has been sent to: #{chief.email}")
    end
  end
end
