class HolidayRequestDecorator < ApplicationDecorator
  delegate_all
  delegate :file, to: :object

  ATTRIBUTE_NAMES_VISIBLE_TO_ALL = %i[
    id applicant_id examiner_id category subcategory visible confirmed applicant_comment
    examiner_comment examiner_comment_history starts_on ends_on accepted_at rejected_at
    absences_count hours non_working_day_request created_at updated_at
  ].freeze

  instance_eval do
    ATTRIBUTE_NAMES_VISIBLE_TO_ALL.each do |field|
      define_method("#{field}") do
        render_attributes_visible_to_all["#{field}"]
      end
    end
  end

  def self.attribute_names_visible_to_all
    ATTRIBUTE_NAMES_VISIBLE_TO_ALL
  end

  def self.db_select_for_collection_for_select
    attribute_names_for_collection_for_select_visible_to_all
  end

  def self.attribute_names_for_collection_for_select_visible_to_all
    [:id, :applicant_id, :examiner_id, :visible, :confirmed, :starts_on, :ends_on, :updated_at]
  end

  def self.resouce_fields_visible_to_all
    attribute_names_visible_to_all + [:problems, :status, :actions_for_current_user, :applicant_name, :examiner_name, :cache_ts]
  end

  def confirmable_category?
    HolidayRequest::CONFIRMABLE_CATEGORIES.include?(category)
  end

  def applicant_name
    helpers.angular_escape(applicant.full_name) if (action_name == 'show' || object.send(:association_instance_get, :applicant).present?) && object.applicant
  end

  def examiner_name
    helpers.angular_escape(examiner.full_name) if (action_name == 'show' || object.send(:association_instance_get, :examiner).present?) && object.examiner
  end

  def cache_ts
    (Time.current.to_f * 1000).ceil
  end

  def problems
    return [] unless ['Niedostępność', 'Niedostępność/Ż'].include?(object.category)
    Array.wrap(::HolidayRequestValidator.new.validate(object))
  end

  def actions_for_current_user
    user = Draper::ViewContext.current.current_user
    bulk_permission_check_hash = {}
    policy = ::HolidayRequestPolicy.new(user, object)
    bulk_permission_check = policy.public_send(:bulk_permission_check,
                                               %w(accept reject convert_to confirmed overuse_of_holidays))
    bulk_permission_check_hash['accept'] = bulk_permission_check[0] || false
    bulk_permission_check_hash['reject'] = bulk_permission_check[1] || false
    bulk_permission_check_hash['convert_to'] = bulk_permission_check[2] || false
    bulk_permission_check_hash['confirmed'] = bulk_permission_check[3] || false
    bulk_permission_check_hash['overuse_of_holidays'] = bulk_permission_check[4] || false
    actions = %w(index show create update destroy)
    actions.each do |action|
      bulk_permission_check_hash[action] = policy.public_send(:"#{action}?")
    end
    bulk_permission_check_hash
  end

  def status
    if accepted_at
      'Accepted'
    elsif rejected_at
      'Rejected'
    else
      'Pending'
    end
  end
end
