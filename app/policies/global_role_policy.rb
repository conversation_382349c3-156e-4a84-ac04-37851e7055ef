class GlobalRolePolicy < ApplicationPolicy
  ALL_AVAILABLE_ATTRIBUTES = [:name, activities: []].freeze

  class Scope < Scope
    def resolve
      scope
    end
  end

  def index?
    allowed_activity?('global_roles:index')
  end

  def show?
    allowed_activity?('global_roles:show')
  end

  def create?
    allowed_activity?('global_roles:create')
  end

  def update?
    global_admin? && allowed_activity?('global_roles:update')
  end

  def destroy?
    global_admin? && allowed_activity?('global_roles:destroy')
  end

  def permitted_attributes
    ALL_AVAILABLE_ATTRIBUTES
  end
end
