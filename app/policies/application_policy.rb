# # http://through-voidness.blogspot.com/2013/10/advanced-rails-4-authorization-with.html
class ApplicationPolicy
  module Mixin # rubocop:disable Metrics/ModuleLength
    include Sanitizers

    def allowed_activity?(activity)
      return false unless signed_in?

      global_roles.map(&:activities).flatten.uniq.include?(activity)
    end

    def native_user?
      signed_in? && user.native?
    end

    def internal_user?
      return false if public?

      signed_in? && user.internal?
    end

    def board_member?
      return false if public?

      signed_in? && user.board_member?
    end

    def global_admin?
      return false if public?

      signed_in? && global_roles.detect(&:global_admin).present?
    end

    def department_chef?
      return false if public?

      user.departments_as_chief.present? || user.departments_as_substitute_chief.present?
    end

    def department_chef_or_uber?
      return false if public?

      user.departments_as_chief.any? || user.departments_as_uber_chief.any? || user.departments_as_supervisor.any?
    end

    def account_manager?
      return false if public?

      Project.supervised_by(user).any?
    end

    def global_admin_programmer?
      return false if public?

      signed_in? && global_roles.detect do |gr|
        gr.name == 'Global Admin Programmer' && gr.global_admin?
      end.present?
    end

    def global_asset_manager?
      return false if public?

      allowed_activity?('assets:manage_all')
    end

    def privileged_user?
      allowed_activity?('users:show') || allowed_activity?('roles:index')
    end

    def hr_user?
      return false if public?

      global_role?(['Global HR Manager'])
    end

    def accounting_user?
      return false if public?

      global_role?(['Global Accounting'])
    end

    def hr_coordinator_user?
      return false if public?

      global_role?(['Global HR Coordinator'])
    end

    def uber_project_manager_user?
      return false if public?

      global_role?(['Global Uber Project Manager'])
    end

    def uber_project_manager_or_project_manager_user?
      return false if public?

      global_role?(['Global Uber Project Manager', 'Global Project Manager'])
    end

    def separate_company?
      user && user.company && HolidayRequest::SEPARATE_COMPANIES.include?(user.company.name.to_s)
    end

    private

    def chief?
      user.departments_as_substitute_chief.any? || user.departments_as_chief.any? ||
        user.departments_as_uber_chief.any? || user.departments_as_supervisor.any?
    end

    def public?
      ::PUBLIC
    end

    def global_role?(positions)
      signed_in? && global_roles.detect { |gr| gr.name.in?(positions) }.present?
    end

    def global_roles
      return unless user

      @global_roles ||= user.global_roles.to_a
    end

    def record_assigned?(opts = {})
      foreign_key = opts[:foreign_key] || :user_id
      linked_record = opts[:linked_record] || record
      if linked_record.respond_to?(:id)
        !!(user && linked_record && linked_record.public_send(foreign_key) && linked_record.public_send(foreign_key) == user.id)
      else
        false
      end
    end

    def record_created_by?(opts = {})
      foreign_key = opts[:foreign_key] || :created_by_user_id
      linked_record = opts[:linked_record] || record
      if linked_record.respond_to?(:id)
        !!(user && linked_record && linked_record.public_send(foreign_key) && linked_record.public_send(foreign_key) == user.id)
      else
        false
      end
    end

    def signed_in?
      user.present? && user.persisted?
    end
  end
  include Mixin

  attr_reader :user, :record

  def initialize(user, record)
    @user = user
    @record = record
  end

  def index?
    raise NotImplementedError
  end

  def show?
    raise NotImplementedError
  end

  def create?
    raise NotImplementedError
  end

  def update?
    raise NotImplementedError
  end

  def destroy?
    raise NotImplementedError
  end

  def permitted_attributes
    raise NotImplementedError
  end

  def scope
    Pundit.policy_scope!(user, record.class)
  end

  class << self
    def bulk_permissions(*action_names)
      define_method(:bulk_permissions) do
        action_names.index_with do |action|
          public_send("#{action}?")
        end.to_h
      end
    end
  end

  class Scope
    include Mixin

    attr_reader :user, :scope

    def initialize(user, scope)
      @user = user
      @scope = scope
    end

    def resolve
      return scope.none unless global_admin?

      scope
    end

    private

    def parent
      scope.instance_variable_get(:@association).owner if collection?
    end

    def collection?
      scope.is_a?(ActiveRecord::Associations::CollectionProxy)
    end
  end
end
