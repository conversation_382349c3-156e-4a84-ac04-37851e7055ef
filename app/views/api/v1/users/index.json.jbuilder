json.array! @users do |user|
  any_active_contract = user.active_contracts.present?
  contracts = any_active_contract ? user.active_contracts : user.user_contracts

  json.partial! '/api/v1/users/user', locals: { user: user }
  json.contract_types contracts.map(&:contract_abbreviation) || []
  json.contract_dates(contracts.map { |c| "#{c.starts_on} - #{c.ends_on || 'Indefinite'}" } || [])
  json.contract_notice_period contracts.pluck(:month_notice_period) || []
  json.any_active_contract any_active_contract
end
