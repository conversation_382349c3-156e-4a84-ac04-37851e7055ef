require 'sidekiq-scheduler'

class PaymentSchedulesReminderWorker
  include Sidekiq::Worker
  include AccountingNotifications

  def perform
    count = settings['repeat_remind_after_days'] || 1
    projects_to_remind.each do |p|
      emails = relevant_user_emails(p)
      next if emails.empty?
      PaymentSchedulesMailer.create_schedule_reminder(p, emails).deliver_now
      p.update_column(:payment_schedule_remind_at, Time.zone.now + count.to_i.days)
    end
    remind_payments
  end

  private

  def remind_payments
    payments_to_remind.each do |payment|
      project = payment.payment_schedule.project
      emails = relevant_user_emails(project)
      next if emails.empty?

      if payment.issued_on < Time.zone.today
        remind_exceeded_payment(payment, project, emails)
      else
        remind_upcoming_payment(payment, project, emails)
      end
    end
  end

  def remind_exceeded_payment(payment, project, emails)
    repeat_after = settings['repeat_remind_exceeded_payments_after'] || 1
    Payment.transaction do
      payment.update_column(:remind_at, Time.zone.now + repeat_after.days)
      PaymentSchedulesMailer.payment_deadline_exceeded(payment, project, emails)
                            .deliver_now
    end
  end

  def remind_upcoming_payment(payment, project, emails)
    remind_after = settings['remind_exceeded_payments_after_days'] || 1
    Payment.transaction do
      remind_at = payment.issued_on + remind_after + Time.zone.now.hour.hours
      payment.update_column(:remind_at, remind_at)
      PaymentSchedulesMailer.upcoming_payment(payment, project, emails)
                            .deliver_now
    end
  end

  def projects_to_remind
    Project.includes(:payment_schedule)
           .where(payment_schedule_required: true)
           .where('payment_schedule_remind_at <= ?', Time.zone.now)
           .where(payment_schedules: { id: nil })
           .active
  end

  def payments_to_remind
    Payment.without_issued_invoice.where('remind_at < ?', Time.zone.now)
           .includes(payment_schedule: :project)
           .where(projects: { status: 0 })
  end
end
