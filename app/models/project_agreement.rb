class ProjectAgreement < ApplicationRecord
  belongs_to :project, optional: true
  belongs_to :company, optional: true
  has_many :approvals, as: :approvable, inverse_of: :approvable, dependent: :destroy
  has_many :attachments, as: :attachable, inverse_of: :attachable, dependent: :destroy

  validates :content, :confirmation_button_text, presence: true

  before_save :put_name
  after_save :update_approvals # , if: :published?

  enum state: %i[draft published]
  before_update :set_published_at

  private

  def set_published_at
    self.published_at = Time.zone.now if state_changed?
  end

  def put_name
    return if state_changed? || changes.blank?
    self.name = define_name
  end

  def define_name
    project.name + ' - ' + translate_b2b + ' - ' + company.name + ' - version ' + find_version.to_s
  end

  def translate_b2b
    business_to_business ? 'B2B' : 'UOP'
  end

  def find_version
    ProjectAgreement.where(project_id: project_id, company_id: company_id, business_to_business: business_to_business).count + 1
  end

  def update_approvals
    return unless published?
    ApprovalsForProjectAgreementWorker.perform_async(id)
  end
end
