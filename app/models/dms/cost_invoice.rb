require 'date_utils'

module Dms
  class CostInvoice < ::CostInvoice
    include LiberalEnum
    include NbpCurrencyTables

    belongs_to :card, optional: true
    belongs_to :cash_payer, class_name: 'User', optional: true

    has_many :cost_projects, inverse_of: :cost_invoice
    has_many :departments, through: :cost_projects
    has_many :cost_invoice_acceptances, dependent: :destroy
    has_many :attachments, as: :attachable, inverse_of: :attachable, dependent: :destroy
    has_many :unaccepted_cost_invoice_acceptances, -> { unaccepted },
             inverse_of: :cost_invoice, class_name: 'CostInvoiceAcceptance'

    enum payment_method: { transfer: 0, card: 1, cash: 2 }
    enum flow: { project: 0, general: 1, simplified: 2 }
    enum state: { draft: 0, pending_department: 1, pending_department_uber: 2,
                  pending_controller: 3, accepted: 4, for_correction: 5, deleted: 6 }
    enum kind: { vat: 0, proforma: 1, correction: 2, accounting_note: 3, vat_barter: 4 }
    liberal_enum :payment_method, :flow, :currency, :kind

    validates :account_number, presence: true, if: -> { transfer? && perform_validations? }
    validates :card, presence: true, if: -> { card? && paid? && perform_validations? }
    validates :paid_on, presence: true, if: -> { paid? && perform_validations? }
    validates :cash_payer, presence: true, if: -> { cash? && perform_validations? }
    validates :payment_method, presence: true, inclusion: { in: payment_methods.keys }
    validates :currency, presence: true, inclusion: { in: currencies.keys }
    validates :flow, presence: true, inclusion: { in: flows.keys }
    validates :kind, presence: true, inclusion: { in: kinds.keys }
    validates :original_document_number, presence: true, if: :correction?
    validates :original_document_number, length: { maximum: 100 }
    validates :description, presence: true
    validates :issuer_comment, length: { maximum: 360 }, allow_nil: true

    validate :projects_amounts_consistency, if: :perform_validations?
    validate :flow_allowed?, if: -> { (current_user || user) && flow_changed? }
    validate :number_uniqueness, if: :perform_validations?
    validate :contractor_state, if: -> { state_changed? && accepted? }
    validate :paid_earlier_than_card_expiration, if: :card?
    validate :cost_projects_cost_account_numbers, if: -> { state_changed? && accepted? }
    validate :overtime_cost_project_changes, if: -> { overtime_invoice? && cost_projects_changed? }
    before_validation :build_overtime_projects_from_service, if: -> { overtime_cost_invoice? && new_record? }

    accepts_nested_attributes_for :cost_projects, allow_destroy: true

    attr_accessor :current_user

    after_save :remove_acceptances, if: :recalculate_department_acceptances?

    after_commit :notify_accepted, if: -> { accepted? && saved_change_to_state? }

    after_commit :notify_rejected, if: -> { for_correction? && saved_change_to_state? }

    after_update :retrieve_exchange_rate, if: -> { currency != 'PLN' }

    after_commit :create_department_acceptances, if: lambda {
      pending_department? && recalculate_department_acceptances?
    }

    after_commit :create_department_uber_acceptances, if: lambda {
      pending_department_uber? && recalculate_department_acceptances?
    }

    after_commit :create_controller_acceptance, if: lambda {
      pending_controller? && recalculate_department_acceptances?
    }

    scope :pending, lambda {
      where(state: %i[pending_department pending_department_uber pending_controller])
    }

    default_scope -> { where('cost_invoices.state != 6') }

    aasm column: :state, enum: true, timestamps: true, whiny_transitions: false do
      state :draft, initial: true
      state :accepted, :pending_department, :for_correction
      state :pending_department
      state :pending_department_uber
      state :pending_controller
      state :deleted

      event :send_to_controller do
        transitions from: %i[draft for_correction], to: :pending_department, guard: :project?
        transitions from: %i[draft for_correction], to: :pending_department_uber, guard: :general?
        transitions from: %i[draft for_correction], to: :pending_controller, guard: :simplified?
      end

      event :accept do
        transitions from: :pending_department, to: :pending_department_uber,
                    guard: :accepted_by_peers?
        transitions from: :pending_department_uber, to: :pending_controller
        transitions from: :pending_controller, to: :accepted
      end

      event :reject do
        transitions from: %i[pending_department pending_department_uber pending_controller],
                    to: :for_correction,
                    after_commit: :delete_acceptances_fully
      end

      event :recall do
        transitions from: :accepted, to: :pending_controller, guard: :accepted_at_today?,
                    after: %i[clear_accepted_at mark_as_unsent]
      end
    end

    has_snapshot_children(&::CostInvoice.has_snapshot_children)
    serialize :removed_contractor_data, Hash

    def editable?
      draft? || for_correction?
    end

    def day_before_invoice_date
      table = determine_table(currency)
      date = invoice_date - 1.day

      if table == 'B'
        # Table B only wednesday 11:45 - 12:15, if wednesday is a holiday, then the previous day
        date -= 1 until date.wednesday?
      end
      date -= 1 while DateUtils.holiday?(date)

      date
    end

    def update_before_invoice_date_currency_rate
      CostInvoiceCurrencyRateWorker.perform_in(5.seconds, day_before_invoice_date.to_s, id, determine_table(currency))
    end

    def exchange_gross_value
      currency_rate = before_invoice_date_currency_rate || 1

      (gross_value * currency_rate).round(2)
    end

    private

    def determine_table(currency)
      return 'A' if TABLE_A_CURRENCIES.include?(currency)
      return 'B' if TABLE_B_CURRENCIES.include?(currency)

      nil
    end

    def retrieve_exchange_rate
      return unless before_invoice_date_currency_rate.nil? || saved_change_to_invoice_date? || currency_previously_changed?

      update_before_invoice_date_currency_rate
    end

    def paid_earlier_than_card_expiration
      return unless card&.expires_on && paid_on
      return if paid_on <= card.expires_on

      errors.add(:paid_on, :later_than_card_expiration, date: card.expires_on)
    end

    def delete_acceptances_fully
      cost_invoice_acceptances.delete_all
    end

    def notify_accepted
      Dms::AcceptedNotifierWorker.perform_async(id)
    end

    def notify_rejected
      Dms::RejectedNotifierWorker.perform_async(id)
    end

    def perform_validations?
      !(draft? || deleted?)
    end

    def notify
      Dms::NotifierWorker.perform_async(id)
    end

    def create_department_acceptances
      create_acceptances(:chief)
    end

    def create_department_uber_acceptances
      create_acceptances(:uber)
    end

    def create_acceptances(kind)
      department_ids = cost_projects.pluck(:department_id)

      department_ids.uniq.each do |department_id|
        cost_invoice_acceptances.create(department_id: department_id, kind: kind)
      end
    end

    def create_controller_acceptance
      cost_invoice_acceptances.create
    end

    def accepted_by_peers?
      unaccepted_cost_invoice_acceptances.none?
    end

    def remove_acceptances
      unaccepted_cost_invoice_acceptances.reload.each(&:delete)
    end

    def projects_amounts_consistency
      return if total_amount == cost_projects.to_a.select(&:amount)
                                             .reject(&:marked_for_destruction?)
                                             .sum(&:amount)

      errors.add(:total_amount, :not_consistant_with_cost_projects)
    end

    def flow_allowed?
      policy = Dms::CostInvoicePolicy.new(current_user || user, self)
      return if policy.allowed_flows.include?(flow.to_sym)

      errors.add(:flow, :not_allowed)
    end

    def number_uniqueness
      scope = Dms::CostInvoice.where(contractor: contractor, number: number)
                              .where.not(kind: :proforma)
                              .where.not(state: %i[draft deleted])
      scope = scope.where.not(id: id) if persisted?
      return if scope.empty?

      errors.add(:number, :should_be_unique)
    end

    def contractor_state
      return if contractor.active?

      errors.add :contractor, :should_be_active
    end

    def cost_projects_cost_account_numbers
      return if cost_projects.includes(:cost_account_number).all?(&:cost_account_number)

      errors.add(:cost_projects, :should_have_cost_account_numbers)
    end

    def recalculate_department_acceptances?
      saved_change_to_state? || cost_projects.any?(&:saved_change_to_department_id?)
    end

    def overtime_cost_invoice? # rubocop:disable Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/PerceivedComplexity
      errors.add(:overtime_selected_month, :month_required) if overtime_invoice? && !overtime_selected_month&.positive?
      errors.add(:overtime_selected_year, :year_required) if overtime_invoice? && !overtime_selected_year&.positive?

      overtime_invoice? && overtime_selected_month&.positive? && overtime_selected_year&.positive?
    end

    def build_overtime_projects_from_service
      Dms::OvertimeProjectDownloader.new(self).call
    end

    def cost_projects_changed?
      return false if new_record?

      cost_projects.any?(&:changed?) && cost_projects.none?(&:new_record?)
    end

    def overtime_cost_project_changes
      return unless current_user
      # global_roles.detect(&:global_admin).present?
      user_policy = UserPolicy.new(nil, current_user)
      return if user_policy.accounting_user? || user_policy.global_admin?

      errors.add(:overtime_invoice, :overtime_changes_require_global_admin)
    end
  end
end
