module Uploaders
  class HolidayRequestUploader < Shrine
    plugin :default_storage, cache: :cache, store: :store

    plugin :versions # (deps: :multi_delete, :default_url) The versions plugin enables your uploader to deal with versions of an image. To generate versions, you simply return a hash of versions like we did in our uploader.

    plugin :activerecord
    plugin :backgrounding

    plugin :remove_attachment

    plugin :restore_cached_data
    plugin :cached_attachment_data

    plugin :remove_invalid
    plugin :data_uri, filename: -> (content_type) do
      extension = MIME::Types[content_type].first.preferred_extension
      "data_uri.#{extension}"
    end
    plugin :determine_mime_type, analyzer: :mimemagic
    plugin :validation_helpers

    # translate error messages: https://github.com/janko-m/shrine/blob/master/lib/shrine/plugins/validation_helpers.rb#L44
    Attacher.validate do
      validate_min_size 10.kilobytes, message: 'is too small (min is 10 KB)'
      validate_max_size 6.megabytes, message: 'is too large (max is 6 MB)' # if record.guest?
      validate_extension_inclusion %w[png jpeg jpg jpe gif bmp tiff tif pdf]
    end

    # /uploads/store/holidayrequest/253/file/3191ad913074a2073dafca71434fe50f # (+ -version if versioned + .ext if existed)
    # plugin :pretty_location, namespace: '/'
    # NOTE: I developed a better solution
    def generate_location(io, context = {})
      type  = context[:record].class.name.underscore
      id    = context[:record].id
      # `slug` specified by the user should be already validated or set by a model callback, #to_url is just a safety fence
      slug  = context[:record].respond_to?(:slug) ? context[:record].slug.to_url : ''
      ctx_name = context[:name]
      style = context[:version]

      extension = ".#{io.extension}" if io.is_a?(UploadedFile) && io.extension
      extension ||= File.extname(extract_filename(io).to_s)
      basename = generate_uid(io)

      real_ext = MIME::Types[context[:metadata]['mime_type']].first
      ext = (real_ext ? ".#{real_ext.preferred_extension}" : extension.to_s)

      name = style.present? ? "#{basename}-#{style}" : basename
      name = slug.present? ? "#{slug}-#{name}" : name
      [type, id, ctx_name, name + ext].compact.join('/')
    end

    HolidayRequest.class_eval do
      attr_accessor :original_filename

      before_save(:set_filename)

      def file_name
        return unless file

        file.data['metadata']['filename']
      end

      def attachment_link
        return unless file

        file.storage.url(file.id, host: APPLICATION_URL)
      end

      def attachment_path
        return unless file

        file.url
      end

      def attachment_api_path
        path = attachment_path
        return unless path

        "#{APPLICATION_URL}/api#{path}"
      end

      def attachment_mime_type
        return unless file

        file.metadata['mime_type']
      end

      private

      def set_filename
        return unless file && original_filename

        file.metadata['filename'] = original_filename
      end
    end
  end
end
