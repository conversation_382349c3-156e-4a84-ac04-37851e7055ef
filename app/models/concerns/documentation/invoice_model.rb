module Documentation
  module InvoiceModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :InvoiceResponses do
        property :InvoiceResponse do
          key :'$ref', :InvoiceResponse
        end
      end
      swagger_schema :InvoiceResponse do
        key :required, [:title, :id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :Invoice
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :title do
          key :type, :string
          key :maximum, 65_535
        end
        property :created_at do
          key :type, :string
          key :maximum, 255
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :url do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :Invoice do
        key :required, [:title]
        property :title do
          key :type, :string
        end
        property :mpk_positions do
          key :required, [:amount, :mpk_number_id]
          key :type, :array
          key :description, 'mpk positions attributes'
          items do
            property :id do
              key :type, :integer
            end
            property :amount do
              key :type, :integer
            end
            property :mpk_number_id do
              key :type, :integer
            end
          end
        end
      end
      swagger_schema :InvoiceRequest do
        key :required, [:invoice]
        property :invoice do
          key :required, [:title]
          key :'$ref', :Invoice
        end
      end
    end
  end
end
