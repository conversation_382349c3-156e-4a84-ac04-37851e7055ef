class CreateUserEntryCards < ActiveRecord::Migration[6.1]
  def change
    create_table :user_entry_cards do |t|
      t.references :user, null: false, foreign_key: true, type: :integer, index: true
      t.integer :card_number, index: true
      t.date :starts_on, index: true
      t.date :ends_on, index: true

      t.timestamps
    end

    remove_index :users, %i[entry_card state]
    remove_column :users, :entry_card, :integer
  end
end
